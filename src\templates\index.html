{% extends "base.html" %}

{% block title %}Dashboard - Terneuzen Terminal Jetty Planning{% endblock %}

{% block header %}<span id="terminal-dashboard-title">Terminal Dashboard</span>{% endblock %}

{% block user_actions %}
    <button class="btn btn-primary" id="refresh-dashboard">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
{% endblock %}

{% block content %}
            <!-- Summary Widgets -->
            <div class="widget-container">
                <div class="widget">
                    <div class="widget-header">
                        <h3 class="widget-title">Vessels Processed</h3>
                        <div class="widget-controls period-toggle" data-active="week" style="margin-left:auto; display:flex; align-items:center; gap:6px; background:#f3f4f6; border-radius:999px; padding:2px; position:relative;">
                            <div class="toggle-slider" aria-hidden="true"></div>
                            <button type="button" class="toggle-btn active" data-period="week" aria-pressed="true" style="border:none; padding:4px 10px; font-size:0.8rem; border-radius:999px; background:transparent; cursor:pointer;">This week</button>
                            <button type="button" class="toggle-btn" data-period="month" aria-pressed="false" style="border:none; padding:4px 10px; font-size:0.8rem; border-radius:999px; background:transparent; cursor:pointer;">This month</button>
                        </div>
                        <i class="fas fa-ship"></i>
                    </div>
                    <div class="widget-value" id="processed-vessels-value">--</div>
                    <div class="widget-footer">
                        <span id="vessel-trend">vs previous period</span>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3 class="widget-title">Active Jetties</h3>
                        <i class="fas fa-anchor"></i>
                    </div>
                    <div class="widget-value">--</div>
                    <div class="widget-footer">
                        <span id="jetty-utilization">Loading...</span>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3 class="widget-title">Today's Throughput</h3>
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="widget-value">--</div>
                    <div class="widget-footer">
                        <i class="fas fa-arrow-up"></i>
                        <span id="throughput-trend">Loading...</span>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3 class="widget-title">Scheduled Operations</h3>
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="widget-value">--</div>
                    <div class="widget-footer">
                        <span>Next 48 hours</span>
                    </div>
                </div>
            </div>

            <!-- Weather Widget -->
            <div class="card">
                <div class="card-header">
                    <h3>Weather Forecast</h3>
                    <a href="/weather" class="btn btn-sm btn-primary">View Details</a>
                </div>
                <div class="card-body">
                    <div class="weather-widget">
                        <div class="weather-current">
                            <div class="weather-icon">
                                <i class="fas fa-cloud-sun" id="dashboard-weather-icon"></i>
                            </div>
                            <div class="weather-info">
                                <h4 id="dashboard-temp">--°C</h4>
                                <p id="dashboard-description">Loading...</p>
                                <small id="dashboard-updated">Updating...</small>
                            </div>
                        </div>
                        <div class="weather-details">
                            <div class="weather-detail">
                                <span class="detail-label">Wind</span>
                                <span id="dashboard-wind">-- m/s</span>
                            </div>
                            <div class="weather-detail">
                                <span class="detail-label">Impact</span>
                                <span id="dashboard-impact"><span class="impact-indicator" id="dashboard-impact-indicator"></span> <span id="dashboard-impact-text">--</span></span>
                            </div>
                            <div class="weather-detail">
                                <span class="detail-label">Thunder</span>
                                <span id="dashboard-thunder">--</span>
                            </div>
                        </div>
                    </div>

                    <div class="weather-forecast" id="dashboard-forecast">
                        <!-- Forecast days will be added here dynamically -->
                        <div class="forecast-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading forecast...</p>
                        </div>
                    </div>

                    <!-- Compact Weather Chips -->
                    <div id="dashboard-weather-chips" style="display:flex; flex-wrap:wrap; gap:8px; margin-top:10px;"></div>

                    
                </div>
            </div>

            <!-- Today's Schedule -->
            <div class="card">
                <div class="card-header">
                    <h3>Today's Schedule</h3>
                    <a href="/schedule" class="btn btn-sm btn-primary">View Full Schedule</a>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Jetty</th>
                                    <th>Vessel</th>
                                    <th>Operation</th>
                                    <th>Product</th>
                                    <th>Volume</th>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                             <tbody id="schedule-table-body">
                                <tr>
                                    <td colspan="8" class="loading-text">Loading schedule...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Incoming Vessels -->
            <div class="card">
                <div class="card-header">
                    <h3>Incoming / Waiting Vessels</h3>
                    <a href="/nominated-vessels" class="btn btn-sm btn-primary">Smart ETA</a>
                </div>
                <div class="card-body">
                    <div class="vessel-list">
                        <div class="loading-text">Loading vessel list...</div>
                    </div>
                </div>
    </div>

    <script nonce="{{ nonce }}">
        // Function to load all dashboard data
        async function loadDashboardData() {
            console.log("Loading all dashboard data...");
            
            // Update terminal-specific info first
            await updateTerminalInfo();
            
            // Load all dashboard components
            await Promise.all([
                loadDashboardMetrics(),
                loadDashboardWeather(),
                loadDashboardSchedule(),
                loadDashboardVessels()
            ]);
            console.log("Dashboard data loading complete.");
        }

        // Function to update terminal-specific information
        async function updateTerminalInfo() {
            try {
                const response = await fetch('/api/terminal');
                if (response.ok) {
                    const terminalData = await response.json();
                    
                    // Update dashboard title
                    const titleElement = document.getElementById('terminal-dashboard-title');
                    if (titleElement) {
                        titleElement.textContent = `${terminalData.name} Dashboard`;
                    }
                    
                    // Store terminal data for other functions
                    window.currentTerminalData = terminalData;
                    
                    console.log('Terminal info updated:', terminalData);
                }
            } catch (error) {
                console.error('Error loading terminal info:', error);
            }
        }

        // Function to load dashboard metrics
        async function loadDashboardMetrics() {
            try {
                console.log('Loading dashboard metrics...');
                
                // Get current terminal statistics
                const response = await fetch('/api/terminals/active/current');
                if (response.ok) {
                    const activeTerminal = await response.json();
                    const statsResponse = await fetch(`/api/terminals/${activeTerminal.id}/statistics`);
                    if (statsResponse.ok) {
                        const stats = await statsResponse.json();
                        updateDashboardWidgets(stats);
                    }

                    // Load processed vessels metric for selected period
                    await updateProcessedVesselsMetric();
                }
            } catch (error) {
                console.error('Error loading dashboard metrics:', error);
            }
        }

        // Function to update dashboard widgets with terminal statistics
        async function updateDashboardWidgets(stats) {
            // Update widget values with real data
            const widgets = document.querySelectorAll('.widget-value');
            if (widgets.length >= 4) {
                // widgets[0] is now controlled by updateProcessedVesselsMetric
                widgets[1].textContent = stats.jetties || 0; // Active Jetties
                widgets[3].textContent = stats.active_assignments || 0; // Scheduled Operations
            }
            
            // Update vessel trend
            const vesselTrend = document.getElementById('vessel-trend');
            if (vesselTrend) {
                const yesterday = stats.vessels_yesterday || 0;
                const today = stats.current_vessels || 0;
                const change = today - yesterday;
                if (change > 0) {
                    vesselTrend.innerHTML = `<i class="fas fa-arrow-up"></i> ${change} vessels since yesterday`;
                } else if (change < 0) {
                    vesselTrend.innerHTML = `<i class="fas fa-arrow-down"></i> ${Math.abs(change)} vessels since yesterday`;
                } else {
                    vesselTrend.textContent = 'No change since yesterday';
                }
            }
            
            // Update jetty utilization
            const utilizationSpan = document.getElementById('jetty-utilization');
            if (utilizationSpan && stats.jetties > 0) {
                const utilization = Math.round((stats.active_assignments / stats.jetties) * 100);
                utilizationSpan.textContent = `${utilization}% utilization`;
            }
            
            // Get real throughput data from today's completed operations
            await updateTodaysThroughput();
        }

        // Compute vessels processed in current period and compare to previous
        async function updateProcessedVesselsMetric() {
            try {
                const activeToggle = document.querySelector('.period-toggle .toggle-btn.active');
                const period = activeToggle ? activeToggle.dataset.period : 'week';
                const now = new Date();

                const startOfCurrent = new Date(now);
                const startOfPrevious = new Date(now);
                let label = 'This week';
                if (period === 'week') {
                    // Set to Monday 00:00 of current week
                    const day = (now.getDay() + 6) % 7; // Mon=0 .. Sun=6
                    startOfCurrent.setDate(now.getDate() - day);
                    startOfCurrent.setHours(0,0,0,0);
                    // Previous week start
                    startOfPrevious.setTime(startOfCurrent.getTime() - 7 * 24 * 3600 * 1000);
                    label = 'This week';
                } else {
                    // First day of current month
                    startOfCurrent.setDate(1);
                    startOfCurrent.setHours(0,0,0,0);
                    // First day of previous month
                    startOfPrevious.setMonth(startOfCurrent.getMonth() - 1);
                    startOfPrevious.setDate(1);
                    startOfPrevious.setHours(0,0,0,0);
                    label = 'This month';
                }

                // Fetch all assignments and count completed whose actual_end_time/end_time in range
                const assignmentsRes = await fetch('/api/schedule/assignments');
                if (!assignmentsRes.ok) throw new Error('Failed to load assignments');
                const assignments = await assignmentsRes.json();

                const inRange = (dt, start, end) => {
                    if (!dt) return false;
                    const d = new Date(dt);
                    return d >= start && d <= now;
                };

                const isCompleted = a => {
                    const s = (a.status || '').toUpperCase();
                    return s === 'COMPLETED' || s === 'ACTIVE' || s === 'IN_PROGRESS';
                };

                // Current period count
                const currentCount = assignments.filter(a => isCompleted(a) && (
                    inRange(a.actual_end_time || a.end_time, startOfCurrent, now)
                )).length;

                // Previous period window end = startOfCurrent - 1ms
                const prevEnd = new Date(startOfCurrent.getTime() - 1);
                // Previous start already computed: startOfPrevious
                const prevCount = assignments.filter(a => isCompleted(a) && (
                    inRange(a.actual_end_time || a.end_time, startOfPrevious, prevEnd)
                )).length;

                // Update UI
                const valEl = document.getElementById('processed-vessels-value');
                if (valEl) valEl.textContent = currentCount;
                const trendEl = document.getElementById('vessel-trend');
                if (trendEl) {
                    const diff = currentCount - prevCount;
                    const sign = diff > 0 ? '+' : diff < 0 ? '−' : '';
                    const arrow = diff > 0 ? 'fa-arrow-up' : diff < 0 ? 'fa-arrow-down' : 'fa-equals';
                    trendEl.innerHTML = `<i class="fas ${arrow}"></i> ${label} vs previous: ${sign}${Math.abs(diff)}`;
                }
            } catch (e) {
                console.error('Failed to update processed vessels metric', e);
                const valEl = document.getElementById('processed-vessels-value');
                if (valEl) valEl.textContent = 'N/A';
                const trendEl = document.getElementById('vessel-trend');
                if (trendEl) trendEl.textContent = 'Data unavailable';
            }
        }
        
        async function updateTodaysThroughput() {
            try {
                const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
                const response = await fetch(`/api/terminals/active/throughput?date=${today}`);
                
                if (response.ok) {
                    const throughputData = await response.json();
                    const widgets = document.querySelectorAll('.widget-value');
                    const throughputTrend = document.getElementById('throughput-trend');
                    
                    if (widgets.length >= 3) {
                        widgets[2].textContent = `${throughputData.total_volume.toLocaleString()} m³`;
                    }
                    
                    if (throughputTrend) {
                        const percentChange = throughputData.percent_change_from_average || 0;
                        const icon = percentChange >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                        const color = percentChange >= 0 ? 'green' : 'red';
                        throughputTrend.innerHTML = `<i class="fas ${icon}" style="color: ${color}"></i> ${Math.abs(percentChange).toFixed(1)}% ${percentChange >= 0 ? 'above' : 'below'} average`;
                    }
                } else {
                    // Fallback: calculate from current assignments
                    const assignmentsResponse = await fetch('/api/schedule/assignments');
                    if (assignmentsResponse.ok) {
                        const assignments = await assignmentsResponse.json();
                        let todayVolume = 0;
                        
                        const today = new Date().toISOString().split('T')[0];
                        assignments.forEach(assignment => {
                            const assignmentDate = new Date(assignment.start_time).toISOString().split('T')[0];
                            if (assignmentDate === today && assignment.cargo_volume) {
                                todayVolume += assignment.cargo_volume;
                            }
                        });
                        
                        const widgets = document.querySelectorAll('.widget-value');
                        if (widgets.length >= 3) {
                            widgets[2].textContent = `${todayVolume.toLocaleString()} m³`;
                        }
                        
                        const throughputTrend = document.getElementById('throughput-trend');
                        if (throughputTrend) {
                            throughputTrend.textContent = 'Based on scheduled operations';
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading throughput data:', error);
                const widgets = document.querySelectorAll('.widget-value');
                const throughputTrend = document.getElementById('throughput-trend');
                
                if (widgets.length >= 3) {
                    widgets[2].textContent = 'N/A';
                }
                if (throughputTrend) {
                    throughputTrend.textContent = 'Data unavailable';
                }
            }
        }

        // Function to refresh all dashboard data (called by button)
        function refreshDashboardData() {
            console.log("Refreshing dashboard data...");
            // Optionally show loading indicators
            document.getElementById('dashboard-description').textContent = 'Refreshing...';
            document.getElementById('dashboard-updated').textContent = 'Updating...';
            document.querySelector('#schedule-table-body').innerHTML = '<tr><td colspan="8" class="loading-text">Refreshing schedule...</td></tr>';
            document.querySelector('.vessel-list').innerHTML = '<div class="loading-text">Refreshing vessel list...</div>';
            // Reload data
            loadDashboardData();
        }

        // Function to load schedule data for the dashboard
        async function loadDashboardSchedule() {
             try {
                console.log('Loading dashboard schedule...');
                const scheduleTableBody = document.getElementById('schedule-table-body');
                if (!scheduleTableBody) return;

                const now = new Date();
                const tomorrow = new Date(now);
                tomorrow.setDate(now.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0); // Start of tomorrow

                // Fetch assignments ending after now and starting before tomorrow
                const response = await fetch(`/api/schedule/assignments?start_before=${tomorrow.toISOString()}&end_after=${now.toISOString()}`);
                const assignments = await response.json();
                console.log('Schedule assignments data:', assignments);

                updateDashboardScheduleTable(assignments);

            } catch (error) {
                console.error("Error loading schedule data:", error);
                const scheduleTableBody = document.getElementById('schedule-table-body');
                if (scheduleTableBody) scheduleTableBody.innerHTML = '<tr><td colspan="8" class="error-text">Error loading schedule</td></tr>';
            }
        }

        // Function to load vessel data for the dashboard
        async function loadDashboardVessels() {
            try {
                console.log('Loading dashboard vessel data...');
                // Use uppercase status values to match backend normalization
                const response = await fetch('/api/vessels?status=APPROACHING&status=WAITING');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const vessels = await response.json();
                console.log('Vessel data:', vessels);
                updateWaitingVessels(vessels);
            } catch (error) {
                console.error("Error loading vessel data:", error);
                const vesselListContainer = document.querySelector('.vessel-list');
                if (vesselListContainer) {
                    // Display user-friendly message instead of error
                    vesselListContainer.innerHTML = '<div class="no-data">No vessels currently waiting or approaching</div>';
                }
            }
        }

        // Function to update the schedule table on the dashboard
        function updateDashboardScheduleTable(assignments) {
            const scheduleTableBody = document.getElementById('schedule-table-body');
            if (!scheduleTableBody) return;

            if (!assignments || assignments.length === 0) {
                scheduleTableBody.innerHTML = '<tr><td colspan="8" class="no-data">No operations scheduled for today</td></tr>';
                return;
            }

             // Sort assignments by start time
            assignments.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));

            const tableContent = assignments.map(a => {
                    const startTime = new Date(a.start_time);
                    const endTime = new Date(a.end_time);
                // Extract product/volume - Needs actual data structure from assignment
                // Extract cargo information (properly linked from vessel data)
                const operation = a.operation || (a.is_loading ? "Loading" : "Unloading");
                const product = a.product || (a.cargo_product || "Unknown");
                const volume = a.volume ? `${a.volume.toLocaleString()} m³` : (a.cargo_volume ? `${a.cargo_volume.toLocaleString()} m³` : "Unknown");

                    return `
                        <tr>
                            <td>${a.jetty_name}</td>
                            <td>${a.vessel_name}</td>
                            <td>${operation}</td>
                            <td>${product}</td>
                            <td>${volume}</td>
                            <td>${startTime.toLocaleTimeString('nl-NL', {hour: '2-digit', minute:'2-digit', hour12: false})}</td>
                            <td>${endTime.toLocaleTimeString('nl-NL', {hour: '2-digit', minute:'2-digit', hour12: false})}</td>
                            <td>
                                <span class="vessel-status ${a.status ? a.status.toLowerCase() : 'unknown'}">${a.status || 'Unknown'}</span>
                            </td>
                        </tr>
                    `;
                }).join('');

                scheduleTableBody.innerHTML = tableContent;
        }

        function updateWaitingVessels(vessels) {
                const vesselListContainer = document.querySelector('.vessel-list');
                if (!vesselListContainer) {
                    console.error("Could not find vessel list container");
                    return;
                }

             // Combine and sort approaching/waiting, prioritize waiting
            const sortedVessels = vessels.sort((a, b) => {
                if (a.status.toLowerCase() === b.status.toLowerCase()) {
                    // If same status, sort by ETA/arrival time (needs actual data)
                    const timeA = new Date(a.eta || a.arrival_time || 0);
                    const timeB = new Date(b.eta || b.arrival_time || 0);
                    return timeA - timeB;
                }
                return a.status.toLowerCase() === 'waiting' ? -1 : 1; // Waiting comes before Approaching
            }).slice(0, 3); // Limit to 3

            if (!sortedVessels || sortedVessels.length === 0) {
                vesselListContainer.innerHTML = '<div class="no-data">No vessels currently waiting or approaching</div>';
                return;
            }

            const vesselCards = sortedVessels.map(v => {
                    const now = new Date();
                let timeInfo = 'ETA';
                let timeValue = 'N/A';

                // Extract cargo details - needs actual cargo data structure
                const product = v.cargoes && v.cargoes.length > 0 ? v.cargoes[0].product : "Unknown Product";
                const volume = v.cargoes && v.cargoes.length > 0 ? `${v.cargoes[0].volume} m³` : "N/A";

                const status = v.status.toLowerCase();
                if (status === 'waiting' && v.arrival_time) {
                    const waitingSince = new Date(v.arrival_time);
                    const diffMs = now - waitingSince;
                    if (diffMs > 0) {
                         const hours = Math.floor(diffMs / (1000 * 60 * 60));
                         const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                         timeInfo = 'Waiting';
                        timeValue = `${hours}h ${minutes}m`;
                    } else {
                        timeInfo = 'Arrived';
                        timeValue = 'Just Now';
                    }

                } else if (status === 'approaching' && v.eta) {
                    const eta = new Date(v.eta);
                    const diffMs = eta - now;
                     if (diffMs > 0) {
                         const hours = Math.floor(diffMs / (1000 * 60 * 60));
                         const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                         timeInfo = 'ETA';
                         timeValue = `${hours}h ${minutes}m`;
                     } else {
                         timeInfo = 'ETA';
                         timeValue = 'Imminent';
                     }
                }

                    return `
                        <div class="vessel-card">
                        <div class="vessel-status ${v.status ? v.status.toLowerCase() : 'unknown'}">${v.status || 'Unknown'}</div>
                            <h4 class="vessel-name">${v.name || 'Unnamed Vessel'}</h4>
                            <div class="vessel-details">
                                <div class="vessel-detail">
                                    <span class="detail-label">Type</span>
                                <span>${v.type || 'N/A'}</span>
                                </div>
                                <div class="vessel-detail">
                                    <span class="detail-label">${timeInfo}</span>
                                    <span>${timeValue}</span>
                                </div>
                                <div class="vessel-detail">
                                    <span class="detail-label">Product</span>
                                    <span>${product}</span>
                                </div>
                                <div class="vessel-detail">
                                    <span class="detail-label">Volume</span>
                                    <span>${volume}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                vesselListContainer.innerHTML = vesselCards;
        }

        // Function to load weather data for the dashboard
        async function loadDashboardWeather() {
            try {
                console.log('Loading dashboard weather data...');
                const updatedEl = document.getElementById('dashboard-updated');
                if(updatedEl) updatedEl.textContent = 'Updating...';

                // Get current weather
                const currentResponse = await fetch('/api/weather');
                 if (!currentResponse.ok) {
                    throw new Error(`HTTP error! status: ${currentResponse.status}`);
                }
                const currentData = await currentResponse.json();
                console.log('Current weather data received:', JSON.stringify(currentData, null, 2)); // Log received current data
                updateDashboardCurrentWeather(currentData);

                // Get 5-day forecast
                const forecastResponse = await fetch('/api/weather?forecast=true&days=5');
                if (!forecastResponse.ok) {
                    throw new Error(`HTTP error! status: ${forecastResponse.status}`);
                }
                const forecastData = await forecastResponse.json();
                console.log('Forecast data received:', JSON.stringify(forecastData, null, 2)); // Log received forecast data
                updateDashboardForecast(forecastData);

                // Try to get next hours for precipitation and thunder risk
                try {
                    const hourlyResp = await fetch('/api/weather?forecast=true&hours=6');
                    if (hourlyResp.ok) {
                        const hourlyData = await hourlyResp.json();
                        updateDashboardWeatherChips(currentData, hourlyData.forecast);
                    } else {
                        updateDashboardWeatherChips(currentData, []);
                    }
                } catch (e) {
                    updateDashboardWeatherChips(currentData, []);
                }

            } catch (error) {
                console.error('Error loading weather data:', error);
                 // Show error in UI elements
                document.getElementById('dashboard-temp').textContent = 'Err'
                document.getElementById('dashboard-description').textContent = 'Error loading'
                document.getElementById('dashboard-updated').textContent = 'Update failed'
                const forecastContainer = document.getElementById('dashboard-forecast');
                if (forecastContainer) {
                    forecastContainer.innerHTML = '<div class="forecast-loading error-text"><p>Error loading forecast</p></div>';
                }
                }
            }

        function updateDashboardCurrentWeather(data) {
            console.log("Attempting to update current weather with data:", JSON.stringify(data, null, 2));
            
            // First try to get current_weather from the data object - API might return it nested under current_weather
            let current;
            
            if (data.current_weather) {
                // Standard structure: { current_weather: {...} }
                current = data.current_weather;
                console.log("Found current_weather object in response");
            } else if (data.temperature !== undefined) {
                // Direct structure: API returns weather properties at the top level
                current = data;
                console.log("Using direct data as current weather (top-level properties)");
            } else if (data.temperature_2m !== undefined) {
                // Alternative structure with _2m suffix
                current = {
                    temperature: data.temperature_2m,
                    wind_speed: data.wind_speed_10m,
                    wind_direction: data.wind_direction_10m,
                    weather_code: data.weather_code,
                    is_day: data.is_day
                };
                console.log("Mapped _2m properties to standard names");
            } else {
                // Handle the response structure where weather data is inside another property
                for (const key in data) {
                    if (data[key] && typeof data[key] === 'object' && (data[key].temperature || data[key].current)) {
                        current = data[key].current || data[key];
                        console.log(`Found weather data in ${key} property`);
                        break;
                    }
                }
            }
            
            // If we still have no valid weather data
            if (!current || Object.keys(current).length === 0) {
                console.warn("No valid weather data found in response:", data);
                document.getElementById('dashboard-temp').textContent = 'N/A'
                document.getElementById('dashboard-description').textContent = 'No data'
                // Reset other fields too
                document.getElementById('dashboard-wind').textContent = 'N/A';
                document.getElementById('dashboard-impact-text').textContent = 'N/A';
                if (document.getElementById('dashboard-impact-indicator')) document.getElementById('dashboard-impact-indicator').className = 'impact-indicator impact-unknown';
                document.getElementById('dashboard-thunder').textContent = 'N/A';
                document.getElementById('vessel-impact-status').textContent = 'Unknown';
                document.getElementById('pumping-impact-status').textContent = 'Unknown';
                return;
            }
            console.log("Using current weather object:", JSON.stringify(current, null, 2));

            // Update icon
            const weatherIcon = document.getElementById('dashboard-weather-icon');
            if (weatherIcon) {
                 // Use is_day from the API response if available, otherwise assume day
                const isDay = current.is_day !== undefined ? current.is_day === 1 : true;
                const weatherCode = current.weather_code;
                console.log(`Current weather code: ${weatherCode}, isDay: ${isDay}`);
                if (weatherCode !== undefined && weatherCode !== null) {
                     weatherIcon.className = getWeatherIconClass(weatherCode, isDay);
                } else {
                    console.warn("Current weather_code is missing.");
                    weatherIcon.className = 'fas fa-question-circle'; // Fallback icon
                }
            } else { console.error("Element #dashboard-weather-icon not found"); }

            // Update temperature and description
            const tempEl = document.getElementById('dashboard-temp');
            if (tempEl) {
                 // Use 'temperature' field
                if (current.temperature !== undefined && current.temperature !== null) {
                    tempEl.textContent = `${Math.round(current.temperature)}°C`;
                } else {
                     console.warn("Current temperature is missing.");
                     tempEl.textContent = 'N/A';
                }
            } else { console.error("Element #dashboard-temp not found"); }

            const descEl = document.getElementById('dashboard-description');
            if (descEl) {
                // Use 'conditions' field, fallback to description helper
                const description = current.conditions || getWeatherDescription(current.weather_code) || 'No description';
                descEl.textContent = description;
                console.log("Setting description:", description);
            } else { console.error("Element #dashboard-description not found"); }

            // Update wind
            const windEl = document.getElementById('dashboard-wind');
            if (windEl) {
                 // Use 'wind_speed' field
                 if (current.wind_speed !== undefined && current.wind_speed !== null) {
                     windEl.textContent = `${current.wind_speed.toFixed(1)} m/s`;
                } else {
                    console.warn("Current wind_speed is missing.");
                    windEl.textContent = 'N/A';
                }
            } else { console.error("Element #dashboard-wind not found"); }

            // Derive wind impact if API didn't supply it
            const impactIndicator = document.getElementById('dashboard-impact-indicator');
            const impactText = document.getElementById('dashboard-impact-text');
            let windImpactClass = 'unknown';
            let windImpactText = 'N/A';
            if (current.wind_impact) {
                windImpactClass = current.wind_impact;
                windImpactText = current.wind_impact === 'low' ? 'Low' : current.wind_impact === 'moderate' ? 'Moderate' : current.wind_impact === 'high' ? 'High' : 'N/A';
            } else if (typeof current.wind_speed === 'number') {
                if (current.wind_speed >= 17.0) { windImpactClass = 'high'; windImpactText = 'High'; }
                else if (current.wind_speed >= 12.0) { windImpactClass = 'moderate'; windImpactText = 'Moderate'; }
                else { windImpactClass = 'low'; windImpactText = 'Low'; }
            }

            console.log(`Wind impact derived: ${windImpactClass} (Text: ${windImpactText})`);
            if (impactIndicator) impactIndicator.className = `impact-indicator impact-${windImpactClass}`;
            if (impactText) impactText.textContent = windImpactText;


            // Derive thunder status from weather code if not provided
            const thunderEl = document.getElementById('dashboard-thunder');
            let thunderTextValue = 'N/A';
            if (current.thunderstorm_impact) {
                thunderTextValue = current.thunderstorm_impact === 'high' ? 'Yes' : current.thunderstorm_impact === 'none' ? 'No' : 'N/A';
            } else if (typeof current.weather_code === 'number') {
                thunderTextValue = (current.weather_code >= 95 && current.weather_code <= 99) ? 'Yes' : 'No';
            }

            console.log(`Thunderstorm risk computed: ${thunderTextValue}`);
            if (thunderEl) thunderEl.textContent = thunderTextValue;

            // Remove operational status updates (section removed)

            // Update last updated time (using timestamp from API if available)
            const now = current.timestamp ? new Date(current.timestamp) : new Date();
            const updatedEl = document.getElementById('dashboard-updated');
            if (updatedEl) updatedEl.textContent = `Updated ${now.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit', hour12: false })}`;
                else { console.error("Element #dashboard-updated not found"); }
        }

        function updateDashboardWeatherChips(currentWrapper, hourly) {
            // currentWrapper may be {current_weather: {...}}
            const current = currentWrapper.current_weather || currentWrapper;
            const container = document.getElementById('dashboard-weather-chips');
            if (!container) return;
            const chips = [];
            // Gusts (if we can infer from hourly max)
            if (hourly && hourly.length > 0) {
                const next6 = hourly.slice(0, 6);
                const maxWind = Math.max(...next6.map(h => Number(h.wind_speed || 0)));
                const gust = Math.max(maxWind, Number(current.wind_speed || 0));
                chips.push(`<span class="chip"><i class="fas fa-wind"></i> Gusts ${gust.toFixed(1)} m/s</span>`);
                // Precip next 3h
                const next3 = next6.slice(0, 3);
                const precip = next3.reduce((s, h) => s + Number(h.precipitation || 0), 0);
                if (precip > 0) chips.push(`<span class="chip"><i class="fas fa-cloud-rain"></i> Precip ${precip.toFixed(1)} mm/3h</span>`);
                // Thunder risk
                const thunderSoon = next6.some(h => (h.weather_code || 0) >= 95);
                if (thunderSoon) chips.push(`<span class="chip danger"><i class="fas fa-bolt"></i> Thunder risk</span>`);
            }
            // Direction
            if (typeof current.wind_direction === 'number') {
                const dirs = ['N','NNE','NE','ENE','E','ESE','SE','SSE','S','SSW','SW','WSW','W','WNW','NW','NNW'];
                const idx = Math.round(current.wind_direction / 22.5) % 16;
                chips.push(`<span class="chip"><i class="fas fa-location-arrow" style="transform: rotate(${current.wind_direction}deg);"></i> ${dirs[idx]}</span>`);
            }
            // Visibility / fog
            if (current.weather_code === 45 || current.weather_code === 48) {
                chips.push(`<span class="chip warn"><i class="fas fa-smog"></i> Reduced visibility</span>`);
            }
            // Sunrise/Sunset if available in daily forecast not here; fallback none
            container.innerHTML = chips.join('');
        }

        function updateDashboardForecast(data) {
            console.log("Attempting to update forecast with data:", JSON.stringify(data, null, 2));
            
            // First try to access forecast data from different possible structures
            let forecast;
            
            if (data.forecast && Array.isArray(data.forecast)) {
                // Standard structure: { forecast: [...] }
                forecast = data.forecast;
                console.log("Found forecast array in data.forecast");
            } else if (Array.isArray(data)) {
                // API might return the forecast array directly
                forecast = data;
                console.log("Data itself is the forecast array");
            } else if (data.daily && Array.isArray(data.daily.time)) {
                // Open-Meteo format
                const { daily } = data;
                forecast = [];
                
                // Convert parallel arrays to array of objects
                for (let i = 0; i < daily.time.length; i++) {
                    forecast.push({
                        date: daily.time[i],
                        max_temp: daily.temperature_2m_max ? daily.temperature_2m_max[i] : null,
                        min_temp: daily.temperature_2m_min ? daily.temperature_2m_min[i] : null,
                        max_wind: daily.wind_speed_10m_max ? daily.wind_speed_10m_max[i] : null,
                        weather_code: daily.weather_code ? daily.weather_code[i] : null
                    });
                }
                console.log("Converted Open-Meteo daily format to forecast array");
            } else {
                // Try to find a forecast array in any of the properties
                for (const key in data) {
                    if (data[key] && Array.isArray(data[key]) && data[key].length > 0 && 
                        data[key][0] && (data[key][0].date || data[key][0].dt || data[key][0].day)) {
                        forecast = data[key];
                        console.log(`Found forecast array in data.${key}`);
                        break;
                    }
                }
            }
            
            const forecastContainer = document.getElementById('dashboard-forecast');
            if (!forecastContainer) {
                console.error("Element #dashboard-forecast not found");
                return;
            }

            // Clear existing content
            forecastContainer.innerHTML = '';

            // If no forecast data could be found
            if (!forecast || !Array.isArray(forecast) || forecast.length === 0) {
                console.warn("No valid forecast array found in response:", data);
                forecastContainer.innerHTML = `
                    <div class="forecast-loading no-data">
                        <i class="fas fa-exclamation-triangle" style="color: #f39c12; margin-bottom: 8px;"></i>
                        <p>Weather forecast temporarily unavailable</p>
                        <small style="color: #7f8c8d;">Data will refresh automatically</small>
                    </div>`;
                return;
            }
            console.log(`Processing ${forecast.length} forecast days.`);

            // Limit to 5 days
            const limitedForecast = forecast.slice(0, 5);

            // Create a forecast day card for each day
            limitedForecast.forEach((day, index) => {
                console.log(`Processing forecast day ${index + 1}:`, JSON.stringify(day, null, 2));
                
                // Extract date from different possible formats
                let dateStr = day.date || day.dt_txt || day.time || (day.dt ? new Date(day.dt * 1000).toISOString().split('T')[0] : null);
                
                if (!dateStr) {
                    console.warn(`Skipping day at index ${index} - no valid date found`);
                    return;
                }
                
                // Format date - handle different date formats
                let date;
                try {
                    // Try standard date string format
                    date = new Date(dateStr);
                    
                    // If date is invalid, try other formats
                    if (isNaN(date.getTime())) {
                        // Try DD-MM-YYYY format
                        if (dateStr.includes('-') && dateStr.split('-').length === 3) {
                            const parts = dateStr.split('-');
                            if (parts[0].length === 2) { // Assuming DD-MM-YYYY
                                date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                            }
                        }
                    }
                    
                    // If still invalid, create a dummy date
                    if (isNaN(date.getTime())) {
                        console.warn(`Invalid date format: ${dateStr}, using current date + index`);
                        date = new Date();
                        date.setDate(date.getDate() + index);
                    }
                } catch (e) {
                    console.error(`Error parsing date: ${dateStr}`, e);
                    // Use a fallback date (today + index)
                    date = new Date();
                    date.setDate(date.getDate() + index);
                }
                
                const dayName = date.toLocaleDateString('nl-NL', { weekday: 'short' });

                // Create forecast day card
                const dayCard = document.createElement('div');
                dayCard.className = 'forecast-day';

                // Weather Code - handle different possible formats
                let weatherCode = day.weather_code;
                
                if (weatherCode === undefined || weatherCode === null) {
                    // Try to get from nested objects
                    if (day.weather && day.weather[0] && day.weather[0].id) {
                        // Convert from OpenWeatherMap ID to our weather code system
                        const owmId = day.weather[0].id;
                        if (owmId >= 200 && owmId < 300) weatherCode = 95; // Thunderstorm
                        else if (owmId >= 300 && owmId < 400) weatherCode = 51; // Drizzle
                        else if (owmId >= 500 && owmId < 600) weatherCode = 61; // Rain
                        else if (owmId >= 600 && owmId < 700) weatherCode = 71; // Snow
                        else if (owmId >= 700 && owmId < 800) weatherCode = 45; // Atmosphere
                        else if (owmId === 800) weatherCode = 0; // Clear
                        else if (owmId > 800) weatherCode = 2; // Clouds
                    } else if (day.hourly && day.hourly.length > 0 && day.hourly[0].weather_code !== undefined) {
                        weatherCode = day.hourly[0].weather_code;
                    } else if (day.conditions && day.conditions.length > 0) {
                        // Try to infer from conditions text
                        const conditionStr = day.conditions[0].toLowerCase();
                        if (conditionStr.includes('thunder')) weatherCode = 95;
                        else if (conditionStr.includes('rain')) weatherCode = 61;
                        else if (conditionStr.includes('snow')) weatherCode = 71;
                        else if (conditionStr.includes('cloud')) weatherCode = 3;
                        else if (conditionStr.includes('clear') || conditionStr.includes('sun')) weatherCode = 0;
                        else weatherCode = 1; // Default to mainly clear
                    } else {
                        // Default if we can't determine
                        weatherCode = 1;
                    }
                }

                // Temperature - try different property names
                let maxTemp = 'N/A';
                if (day.max_temp !== undefined && day.max_temp !== null) {
                    maxTemp = `${Math.round(day.max_temp)}°C`;
                } else if (day.temp && day.temp.max !== undefined) {
                    maxTemp = `${Math.round(day.temp.max)}°C`;
                } else if (day.main && day.main.temp_max !== undefined) {
                    maxTemp = `${Math.round(day.main.temp_max)}°C`;
                } else if (day.temperature !== undefined) {
                    maxTemp = `${Math.round(day.temperature)}°C`;
                }

                // Wind - try different property names
                let maxWind = 'N/A';
                if (day.max_wind !== undefined && day.max_wind !== null) {
                    maxWind = `${parseFloat(day.max_wind).toFixed(1)} m/s`;
                } else if (day.wind && day.wind.speed !== undefined) {
                    maxWind = `${parseFloat(day.wind.speed).toFixed(1)} m/s`;
                } else if (day.wind_speed !== undefined) {
                    maxWind = `${parseFloat(day.wind_speed).toFixed(1)} m/s`;
                }

                // Get icon class (assuming daytime for forecast for simplicity)
                const iconClass = getWeatherIconClass(weatherCode, true);
                console.log(`Forecast day ${index + 1}: Date=${day.date}, Code=${weatherCode}, Icon=${iconClass}, Temp=${maxTemp}, Wind=${maxWind}`);

                dayCard.innerHTML = `
                    <span class="day-name">${dayName}</span>
                    <div class="weather-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <span class="temp">${maxTemp}</span>
                    <span class="wind">${maxWind}</span>
                `;

                forecastContainer.appendChild(dayCard);
            });
             // Add check if no valid days were processed
            if (forecastContainer.childElementCount === 0) {
                 console.warn("Forecast data was present, but no valid days could be rendered.");
                 forecastContainer.innerHTML = '<div class="forecast-loading no-data"><p>Could not display forecast data</p></div>';
            }
        }

        function getWeatherIconClass(code, isDay) {
            // Map WMO weather codes to Font Awesome icons
            const dayIcons = {
                0: 'fas fa-sun', // Clear sky
                1: 'fas fa-sun', // Mainly clear
                2: 'fas fa-cloud-sun', // Partly cloudy
                3: 'fas fa-cloud', // Overcast
                45: 'fas fa-smog', // Fog
                48: 'fas fa-smog', // Depositing rime fog
                51: 'fas fa-cloud-rain', // Light drizzle
                53: 'fas fa-cloud-rain', // Moderate drizzle
                55: 'fas fa-cloud-rain', // Dense drizzle
                56: 'fas fa-cloud-rain', // Light freezing drizzle
                57: 'fas fa-cloud-rain', // Dense freezing drizzle
                61: 'fas fa-cloud-rain', // Slight rain
                63: 'fas fa-cloud-rain', // Moderate rain
                65: 'fas fa-cloud-showers-heavy', // Heavy rain
                66: 'fas fa-cloud-rain', // Light freezing rain
                67: 'fas fa-cloud-showers-heavy', // Heavy freezing rain
                71: 'fas fa-snowflake', // Slight snow fall
                73: 'fas fa-snowflake', // Moderate snow fall
                75: 'fas fa-snowflake', // Heavy snow fall
                77: 'fas fa-snowflake', // Snow grains
                80: 'fas fa-cloud-rain', // Slight rain showers
                81: 'fas fa-cloud-showers-heavy', // Moderate rain showers
                82: 'fas fa-cloud-showers-heavy', // Violent rain showers
                85: 'fas fa-snowflake', // Slight snow showers
                86: 'fas fa-snowflake', // Heavy snow showers
                95: 'fas fa-bolt', // Thunderstorm
                96: 'fas fa-bolt', // Thunderstorm with slight hail
                99: 'fas fa-bolt', // Thunderstorm with heavy hail
            };

            const nightIcons = {
                0: 'fas fa-moon', // Clear sky
                1: 'fas fa-moon', // Mainly clear
                2: 'fas fa-cloud-moon', // Partly cloudy
                3: 'fas fa-cloud', // Overcast
                // Fallback to day icons for precipitation/fog/etc. at night
                45: dayIcons[45], 48: dayIcons[48],
                51: dayIcons[51], 53: dayIcons[53], 55: dayIcons[55], 56: dayIcons[56], 57: dayIcons[57],
                61: dayIcons[61], 63: dayIcons[63], 65: dayIcons[65], 66: dayIcons[66], 67: dayIcons[67],
                71: dayIcons[71], 73: dayIcons[73], 75: dayIcons[75], 77: dayIcons[77],
                80: dayIcons[80], 81: dayIcons[81], 82: dayIcons[82],
                85: dayIcons[85], 86: dayIcons[86],
                95: dayIcons[95], 96: dayIcons[96], 99: dayIcons[99],
            };

            // Use night icons if applicable and available, otherwise default to day icon
            const icons = !isDay ? nightIcons : dayIcons;
            return icons[code] || dayIcons[code] || 'fas fa-question-circle'; // Fallback icon
        }

        function getWeatherDescription(code) {
            const descriptions = {
                0: 'Clear sky', 1: 'Mainly clear', 2: 'Partly cloudy', 3: 'Overcast',
                45: 'Fog', 48: 'Rime fog',
                51: 'Light drizzle', 53: 'Moderate drizzle', 55: 'Dense drizzle',
                56: 'Light freezing drizzle', 57: 'Dense freezing drizzle',
                61: 'Slight rain', 63: 'Moderate rain', 65: 'Heavy rain',
                66: 'Light freezing rain', 67: 'Heavy freezing rain',
                71: 'Slight snow fall', 73: 'Moderate snow fall', 75: 'Heavy snow fall',
                77: 'Snow grains',
                80: 'Slight rain showers', 81: 'Moderate rain showers', 82: 'Violent rain showers',
                85: 'Slight snow showers', 86: 'Heavy snow showers',
                95: 'Thunderstorm', 96: 'Thunderstorm, slight hail', 99: 'Thunderstorm, heavy hail'
            };
            return descriptions[code];
        }

        // Listen for terminal changes
        document.addEventListener('terminalChanged', function(event) {
            console.log('Terminal changed on dashboard:', event.detail);
            // Reload all dashboard data for the new terminal
            loadDashboardData();
        });

        // Listen for page refresh requests
        document.addEventListener('refreshPageData', function(event) {
            console.log('Refreshing dashboard data:', event.detail);
            refreshDashboardData();
        });

        // Wire up refresh button without inline handler
        document.addEventListener('DOMContentLoaded', function() {
            const refreshBtn = document.getElementById('refresh-dashboard');
            if (refreshBtn) refreshBtn.addEventListener('click', refreshDashboardData);
        });

        // Initial data load on page ready
        document.addEventListener('DOMContentLoaded', loadDashboardData);

        // Wire segmented period toggle buttons
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.period-toggle');
            if (!container) return;
            const buttons = container.querySelectorAll('.toggle-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => { b.classList.remove('active'); b.setAttribute('aria-pressed', 'false'); });
                    btn.classList.add('active');
                    btn.setAttribute('aria-pressed', 'true');
                    // Update data-active to drive slider position
                    const period = btn.dataset.period === 'month' ? 'month' : 'week';
                    container.setAttribute('data-active', period);
                    updateProcessedVesselsMetric();
                });
            });
        });

    </script>
{% endblock %}
